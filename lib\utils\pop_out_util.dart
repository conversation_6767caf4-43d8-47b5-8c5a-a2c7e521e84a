import 'dart:io';

import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/common/display_image.dart';
import 'package:ddone/components/common/display_video.dart';
import 'package:ddone/components/common/qr_code_scanner.dart';
import 'package:ddone/components/dialog/add_contact.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/screens/transfer_dialpad.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/permission_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:heif_converter/heif_converter.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:path/path.dart' as p;

Future<T?> showCustomBottomSheet<T>(
  BuildContext context, {
  required Widget Function(BuildContext) builder,
  bool removePadding = false,
  Color backgroundColor = Colors.white,
  double radius = radiusMedium,
}) async {
  return await showModalBottomSheet(
    context: context,
    backgroundColor: backgroundColor,
    builder: builder,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(radius),
        topRight: Radius.circular(radius),
      ),
    ),
  );
}

void showSnackBarWithText(BuildContext context, String msg) {
  //dismiss current snackbar first before showing another
  ScaffoldMessenger.of(context).removeCurrentSnackBar();

  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      margin: const EdgeInsets.symmetric(
        horizontal: spacingExtraLarge,
        vertical: spacingLarge,
      ),
      content: Text(
        msg,
        style: const TextStyle(color: Colors.white),
        textAlign: TextAlign.center,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusSmall),
      ),
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.black,
    ),
  );
}

void errorSnackBar(BuildContext context, String? errorText) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      width: context.deviceWidth(0.5),
      duration: const Duration(
        seconds: 5,
      ),
      content: Stack(
        children: [
          Container(
            padding: const EdgeInsets.all(
              16,
            ),
            decoration: const BoxDecoration(
              color: Color(0xFFC72C41),
              borderRadius: BorderRadius.all(
                Radius.circular(
                  20,
                ),
              ),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 38,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Whoops!'),
                      Text(
                        errorText ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 11,
            left: 10,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
              ),
              child: Image.asset(
                '$imagePathPrefix/dot_dash_logo.png',
                width: 40,
                height: 48,
                color: const Color(0xFF801336),
              ),
            ),
          ),
          Positioned(
            bottom: 11,
            right: 10,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
              ),
              child: Image.asset(
                '$imagePathPrefix/dot_dash_logo.png',
                width: 40,
                height: 48,
                color: const Color(0xFF801336),
              ),
            ),
          )
        ],
      ),
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      elevation: 0,
    ),
  );
}

void imagePreviewDialog({
  required BuildContext context,
  required String imageUrl,
}) {
  showDialog(
      useSafeArea: true,
      context: context,
      builder: (context) {
        return DisplayImage(
          imageUrl: imageUrl,
        );
      });
}

void videoPreviewDialog({
  required BuildContext context,
  required String videoUrl,
}) {
  showDialog(
      useSafeArea: true,
      barrierDismissible: true,
      context: context,
      builder: (context) {
        return DisplayVideo(
          videoUrl: videoUrl,
        );
      });
}

Future<void> showAddContactDialog(
  BuildContext context, {
  String? contactName,
  String? contactNumber,
  bool isEdit = false,
}) async {
  await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (dialogContext) {
      return AddContactDialog(
        contactName: contactName,
        contactNumber: contactNumber,
        isEdit: isEdit,
      );
    },
  );
}

Future<T?> showPopUpMenu<T>({
  required BuildContext context,
  required RelativeRect position,
  required List<PopupMenuEntry<T>> items,
  T? initialValue,
  double elevation = 8.0,
  Color? shadowColor,
  ShapeBorder? shape,
  Color? color,
}) {
  return showMenu<T>(
    context: context,
    position: position,
    items: items,
    initialValue: initialValue,
    elevation: elevation,
    shadowColor: shadowColor,
    shape: shape,
    color: color,
  );
}

enum ButtonTapped { left, right }

Future<ButtonTapped?> showConfirmDialog({
  required BuildContext context,
  String? desc,
  required String title,
  required String rightButtonText,
  bool barrierDismissible = true,
  TextStyle? rightButtonTextStyle,
  TextStyle? titleTextStyle,
  TextStyle? descTextStyle,
  String? leftButtonText,
  Widget? descWidget,
  VoidCallback? onPressedLeftButton,
  VoidCallback? onPressedRightButton,
}) async {
  return await showDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) => BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final textTheme = themeState.themeData.textTheme;
        final colorTheme = themeState.colorTheme;

        final primaryColor = colorTheme.primaryColor;

        final bodyMedium = textTheme.bodyMedium;

        return AlertDialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(spacingExtraLarge),
            ),
          ),
          title: Text(
            title,
            style: titleTextStyle ?? textTheme.titleMedium!.copyWith(color: primaryColor),
            textAlign: TextAlign.center,
          ),
          content: descWidget ??
              Text(
                desc ?? '',
                style: descTextStyle ?? bodyMedium,
                textAlign: TextAlign.center,
              ),
          actions: <Widget>[
            if (leftButtonText != null)
              TextButton(
                onPressed: () {
                  if (onPressedLeftButton != null) onPressedLeftButton();

                  pop(ButtonTapped.left);
                },
                child: Text(
                  leftButtonText,
                  style: rightButtonTextStyle ?? bodyMedium?.copyWith(color: colorTheme.errorColor),
                ),
              ),
            TextButton(
              onPressed: () {
                if (onPressedRightButton != null) onPressedRightButton();

                pop(ButtonTapped.left);
              },
              child: Text(
                rightButtonText,
                style: rightButtonTextStyle ?? bodyMedium!.copyWith(color: colorTheme.connectedColor),
              ),
            ),
          ],
        );
      },
    ),
  );
}

void showTransferDialpadDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.transparent,
    builder: (context) {
      return AlertDialog(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(32.0),
          ),
        ),
        insetPadding: const EdgeInsets.all(10),
        backgroundColor: const Color.fromARGB(255, 54, 54, 54),
        content: SizedBox(
          width: context.deviceWidth(isDesktop ? 0.85 : 1.0),
          child: const Column(
            mainAxisSize: MainAxisSize.max,
            children: [TransferDialpad()],
          ),
        ),
      );
    },
  );
}

void showOnholdCallDialog(
  BuildContext context, {
  required GestureTapCallback onTap,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;

          return AlertDialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(32.0),
              ),
            ),
            insetPadding: const EdgeInsets.all(10),
            backgroundColor: colorTheme.roundShapeInkWellColor,
            content: SizedBox(
              width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  const Text(
                    'Call on hold. Press the button below to unhold.',
                    style: TextStyle(color: Colors.orange),
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                  RoundShapeInkWell(
                    onTap: onTap,
                    color: colorTheme.primaryColor,
                    contentWidget: const Icon(Icons.play_arrow),
                  ),
                ],
              ),
            ),
          );
        },
      );
    },
  );
}

Future<List<String>> showCameraGalleryBottomSheetMultiple(
  BuildContext context, {
  Color backgroundColor = Colors.transparent,
  bool showQr = false,
  bool showCamera = true,
  bool showGallery = true,
  bool showFiles = true,
  Function(String?)? qrDataCallBack,
  Function(List<String>?)? photoListCallBack,
  List<String> customExtensionAllowed = const [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'pdf',
    'heic',
    'heif',
  ],
  bool allowedMultipick = false,
  double maxFileSizeMB = 25.0,
}) async {
  List<String> pathList = [];

  // Helper function to show error dialogs
  Future<void> showErrorDialog(BuildContext context, String title, String message) async {
    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Helper function to convert HEIC/HEIF to PNG
  Future<String?> convertHeicToPng(String heicPath, BuildContext context) async {
    try {
      // Check if the file is HEIC/HEIF
      final extension = heicPath.split('.').last.toLowerCase();
      if (!['heic', 'heif'].contains(extension)) {
        return heicPath; // Return original path if not HEIC/HEIF
      }

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Converting HEIC/HEIF to PNG...'),
              ],
            ),
          );
        },
      );

      final tempDir = await getTemporaryDirectory();
      final fileName = p.basenameWithoutExtension(heicPath);
      final pngPath = p.join(tempDir.path, '${fileName}_converted.png');

      // Convert HEIC to PNG
      final String? finalPngPath = await HeifConverter.convert(
        heicPath,
        output: pngPath,
      );

      pop(); // Close loading dialog

      if (finalPngPath != null) {
        return finalPngPath;
      } else {
        if (context.mounted) {
          await showErrorDialog(
            context,
            'Processing Failed',
            'Failed to process HEIC image. Please try a different image.',
          );
        }
        return null;
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted && Navigator.of(context).canPop()) {
        pop();
      }

      if (context.mounted) {
        await showErrorDialog(
          context,
          'Processing Error',
          'An error occurred while processing the image: ${e.toString()}',
        );
      }
      return null;
    }
  }

  // Helper function to validate file size and extension
  // Helper function to validate and process file (includes HEIC conversion)
  Future<String?> validateAndProcessFile(String filePath, List<String> allowedExtensions, BuildContext context) async {
    final extension = filePath.split('.').last.toLowerCase();
    final file = File(filePath);

    // Check if file exists
    if (!await file.exists()) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'File Not Found',
          'The selected file could not be found.',
        );
      }
      return null;
    }

    // Convert HEIC/HEIF to PNG if needed
    String processedPath = filePath;
    if (['heic', 'heif'].contains(extension) && context.mounted) {
      final convertedPath = await convertHeicToPng(filePath, context);
      if (convertedPath == null) {
        return null; // Conversion failed
      }
      processedPath = convertedPath;
    }

    // Validate the processed file
    final processedFile = File(processedPath);
    final processedExtension = processedPath.split('.').last.toLowerCase();
    final sizeInMb = processedFile.lengthSync() / (1024 * 1024);

    // Check extension (after conversion)
    if (allowedExtensions.isNotEmpty && !allowedExtensions.contains(processedExtension)) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'Unsupported File Type',
          '.$processedExtension file type is not supported',
        );
      }
      return null;
    }

    // Check file size
    if (sizeInMb > maxFileSizeMB) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'File Too Large',
          '${processedFile.path.split('/').last}\nFile size exceeds the limit: ${sizeInMb.toStringAsFixed(2)}MB (Max: ${maxFileSizeMB}MB)',
        );
      }
      return null;
    }

    return processedPath;
  }

  // Helper function to handle QR scanning
  Future<void> handleQrScan(BuildContext context) async {
    try {
      if (!await PermissionUtil.handlePermissionRequest(Permission.camera, context,
          customTitle: 'Camera Access Required',
          customMessage: 'We need camera access to scan QR. Please grant permission in settings.')) return;

      pop(); // Close bottom sheet

      if (context.mounted) {
        String? qrCode = await Navigator.of(context).pushNamed(QRCodeScannerPage.routeName) as String?;
        qrDataCallBack?.call(qrCode);
      }
    } catch (e) {
      if (context.mounted) {
        await showDialog(
          context: context,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: const Text('Error'),
              content: const Text('Failed to scan QR code. Please try again.'),
              actions: [
                TextButton(
                  onPressed: () => pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    }
  }

  // Helper function to handle camera capture
  Future<void> handleCamera(BuildContext context) async {
    try {
      if (!await PermissionUtil.handlePermissionRequest(Permission.camera, context,
          customTitle: 'Camera Access Required',
          customMessage: 'We need camera access to scan QR. Please grant permission in settings.')) return;

      final picker = ImagePicker();
      final photo = await picker.pickImage(source: ImageSource.camera);

      if (photo != null && context.mounted) {
        final processedPath = await validateAndProcessFile(photo.path, customExtensionAllowed, context);
        if (processedPath != null) {
          pathList.add(processedPath);
          pop(); // Close bottom sheet
        }
      }
    } catch (e) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'Error',
          'Failed to capture photo. Please try again.',
        );
      }
    }
  }

  // Helper function to handle gallery selection
  Future<void> handleGallery(BuildContext context) async {
    try {
      final DeviceInfoPlugin info = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await info.androidInfo;
      final int androidVersion = int.parse(androidInfo.version.release);
      if (androidVersion >= 13) {
        if (!await PermissionUtil.handlePermissionRequest(Permission.photos, context,
            customTitle: 'Photos Access Required',
            customMessage: 'We need access to your photos to select images. Please grant permission in settings.')) {
          return;
        }
      }

      final picker = ImagePicker();
      List<String> validPaths = [];
      List<String> invalidFiles = [];

      if (allowedMultipick) {
        final photoList = await picker.pickMultiImage();
        if (photoList.isNotEmpty && context.mounted) {
          for (final photo in photoList) {
            final processedPath = await validateAndProcessFile(photo.path, customExtensionAllowed, context);
            if (processedPath != null) {
              validPaths.add(processedPath);
            } else {
              // Get file info for error reporting
              final extension = photo.path.split('.').last.toLowerCase();
              final sizeInMb = File(photo.path).lengthSync() / (1024 * 1024);

              if (['heic', 'heif'].contains(extension)) {
                invalidFiles.add('${photo.name} (HEIC conversion failed)');
              } else if (sizeInMb > maxFileSizeMB) {
                invalidFiles.add('${photo.name} (${sizeInMb.toStringAsFixed(2)}MB)');
              } else {
                invalidFiles.add('${photo.name} (processing failed)');
              }
            }
          }
        }
      } else {
        final photo = await picker.pickImage(source: ImageSource.gallery);
        if (photo != null && context.mounted) {
          final processedPath = await validateAndProcessFile(photo.path, customExtensionAllowed, context);
          if (processedPath != null) {
            validPaths.add(processedPath);
          }
        }
      }

      // Add valid paths
      pathList.addAll(validPaths);

      // Show errors for invalid files if any (only for multi-select)
      if (invalidFiles.isNotEmpty && allowedMultipick && context.mounted) {
        await showDialog(
          context: context,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: const Text('Some Files Invalid'),
              content: SingleChildScrollView(
                child: Text('The following files could not be processed:\n\n${invalidFiles.join('\n')}'),
              ),
              actions: [
                TextButton(
                  onPressed: () => pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }

      if (validPaths.isNotEmpty) {
        pop(); // Close bottom sheet
      }
    } catch (e) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'Error',
          'Failed to select from gallery. Please try again.',
        );
      }
    }
  }

  // Helper function to handle file picker
  Future<void> handleFilePicker(BuildContext context) async {
    try {
      // Note: FilePicker might need storage permission on some Android versions
      // You might want to request Permission.storage here for older Android versions

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: customExtensionAllowed.isEmpty
            ? ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'heic', 'heif']
            : customExtensionAllowed,
        allowMultiple: allowedMultipick,
      );

      if (result != null) {
        List<String> validPaths = [];
        List<String> invalidFiles = [];

        for (final platformFile in result.files) {
          if (platformFile.path != null && context.mounted) {
            final processedPath = await validateAndProcessFile(platformFile.path!, customExtensionAllowed, context);
            if (processedPath != null) {
              validPaths.add(processedPath);
            } else {
              // Get file info for error reporting
              final extension = platformFile.path!.split('.').last.toLowerCase();
              final sizeInMb = File(platformFile.path!).lengthSync() / (1024 * 1024);

              if (['heic', 'heif'].contains(extension)) {
                invalidFiles.add('${platformFile.name} (HEIC conversion failed)');
              } else if (sizeInMb > maxFileSizeMB) {
                invalidFiles.add('${platformFile.name} (${sizeInMb.toStringAsFixed(2)}MB)');
              } else {
                invalidFiles.add('${platformFile.name} (processing failed)');
              }
            }
          }
        }

        // Add valid paths
        pathList.addAll(validPaths);

        // Show errors for invalid files if any
        if (invalidFiles.isNotEmpty && context.mounted) {
          await showDialog(
            context: context,
            builder: (BuildContext dialogContext) {
              return AlertDialog(
                title: const Text('Some Files Invalid'),
                content: SingleChildScrollView(
                  child: Text('The following files could not be processed:\n\n${invalidFiles.join('\n')}'),
                ),
                actions: [
                  TextButton(
                    onPressed: () => pop(),
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }

        if (validPaths.isNotEmpty) {
          pop(); // Close bottom sheet
        }
      }
    } catch (e) {
      if (context.mounted) {
        await showErrorDialog(
          context,
          'Error',
          'Failed to select files. Please try again.',
        );
      }
    }
  }

  await showCustomBottomSheet(
    context,
    builder: (_) => BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Container(
          width: double.infinity,
          height: 240,
          color: colorTheme.backgroundColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showQr)
                _buildOptionButton(
                  onPressed: () => handleQrScan(context),
                  icon: Icons.qr_code,
                  text: 'Scan QR',
                  textTheme: textTheme,
                  colorTheme: colorTheme,
                ),
              if (showCamera)
                _buildOptionButton(
                  onPressed: () => handleCamera(context),
                  icon: Icons.camera,
                  text: 'Camera',
                  textTheme: textTheme,
                  colorTheme: colorTheme,
                ),
              if (showGallery)
                _buildOptionButton(
                  onPressed: () => handleGallery(context),
                  icon: Icons.image,
                  text: 'Upload from gallery',
                  textTheme: textTheme,
                  colorTheme: colorTheme,
                ),
              if (showFiles)
                _buildOptionButton(
                  onPressed: () => handleFilePicker(context),
                  icon: Icons.folder,
                  text: 'Upload from files',
                  textTheme: textTheme,
                  colorTheme: colorTheme,
                ),
            ],
          ),
        );
      },
    ),
    backgroundColor: backgroundColor,
    radius: radiusMedium,
  );

  photoListCallBack?.call(pathList);
  return pathList;
}

// Helper widget to build option buttons
Widget _buildOptionButton({
  required VoidCallback onPressed,
  required IconData icon,
  required String text,
  required TextTheme textTheme,
  required dynamic colorTheme,
}) {
  return SizedBox(
    height: heightMedium,
    width: double.infinity,
    child: TextButton(
      onPressed: onPressed,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: spacingMedium),
        child: Row(
          children: [
            Icon(icon),
            const SizedBox(width: spacingLarge),
            Text(
              text,
              style: textTheme.titleSmall!.copyWith(
                color: colorTheme.onBackgroundColor,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
