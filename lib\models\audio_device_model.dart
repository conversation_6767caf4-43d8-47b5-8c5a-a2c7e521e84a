import 'package:ddone/models/enums/audio_device_type_enum.dart';
import 'package:equatable/equatable.dart';

class AudioDeviceModel extends Equatable {
  final String deviceId;
  final String label;
  final AudioDeviceType type;
  final bool isSelected;
  final bool isAvailable;

  const AudioDeviceModel({
    required this.deviceId,
    required this.label,
    required this.type,
    this.isSelected = false,
    this.isAvailable = true,
  });

  AudioDeviceModel copyWith({
    String? deviceId,
    String? label,
    AudioDeviceType? type,
    bool? isSelected,
    bool? isAvailable,
  }) {
    return AudioDeviceModel(
      deviceId: deviceId ?? this.deviceId,
      label: label ?? this.label,
      type: type ?? this.type,
      isSelected: isSelected ?? this.isSelected,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  @override
  List<Object?> get props => [
        deviceId,
        label,
        type,
        isSelected,
        isAvailable,
      ];

  @override
  String toString() {
    return 'AudioDeviceModel(deviceId: $deviceId, label: $label, type: $type, isSelected: $isSelected, isAvailable: $isAvailable)';
  }

  /// Factory method to create AudioDeviceModel from MediaDeviceInfo
  static AudioDeviceModel fromMediaDeviceInfo(
    String deviceId,
    String label, {
    bool isSelected = false,
  }) {
    final type = _determineDeviceType(deviceId, label);
    return AudioDeviceModel(
      deviceId: deviceId,
      label: label.isEmpty ? type.displayName : label,
      type: type,
      isSelected: isSelected,
    );
  }

  /// Determine device type based on deviceId and label
  static AudioDeviceType _determineDeviceType(String deviceId, String label) {
    final lowerDeviceId = deviceId.toLowerCase();
    final lowerLabel = label.toLowerCase();

    // iOS specific device IDs
    if (deviceId == 'Built-In Receiver' || lowerDeviceId.contains('receiver')) {
      return AudioDeviceType.earpiece;
    }
    if (deviceId == 'Speaker' || lowerDeviceId.contains('speaker')) {
      return AudioDeviceType.speaker;
    }

    // Android specific device IDs
    if (lowerDeviceId == 'earpiece') {
      return AudioDeviceType.earpiece;
    }
    if (lowerDeviceId == 'speaker') {
      return AudioDeviceType.speaker;
    }

    // Bluetooth devices
    if (lowerLabel.contains('bluetooth') ||
        lowerDeviceId.contains('bluetooth') ||
        lowerLabel.contains('bt') ||
        lowerDeviceId.contains('bt') ||
        lowerLabel.contains('airpods') ||
        lowerDeviceId.contains('airpods') ||
        lowerLabel.contains('headset') ||
        lowerDeviceId.contains('headset')) {
      if (lowerLabel.contains('headphone') ||
          lowerDeviceId.contains('headphone') ||
          lowerLabel.contains('headset') ||
          lowerDeviceId.contains('headset') ||
          lowerLabel.contains('airpods') ||
          lowerDeviceId.contains('airpods') ||
          lowerLabel.contains('earbuds') ||
          lowerDeviceId.contains('earbuds')) {
        return AudioDeviceType.bluetoothHeadphones;
      }
      return AudioDeviceType.bluetoothSpeaker;
    }

    // Wired devices
    if (lowerLabel.contains('wired') ||
        lowerLabel.contains('headphone') ||
        lowerLabel.contains('headset') ||
        lowerLabel.contains('earphone')) {
      return AudioDeviceType.wiredHeadphones;
    }

    // Default/system devices
    if (lowerLabel.contains('default') || lowerLabel.contains('system')) {
      return AudioDeviceType.systemDefault;
    }

    if (lowerLabel.contains('speaker')) {
      return AudioDeviceType.speaker;
    }

    return AudioDeviceType.other;
  }
}
