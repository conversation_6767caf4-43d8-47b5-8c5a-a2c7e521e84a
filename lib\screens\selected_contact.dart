import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/components/common/common_card.dart';
import 'package:ddone/components/common/round_shape_avatar.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:ddone/models/hive/local_contact.dart';
import 'package:logger/logger.dart';

class SelectedContact extends StatefulWidget {
  static const routeName = '/selectedContact';

  const SelectedContact({
    super.key,
  });

  @override
  State<SelectedContact> createState() => _SelectedContactState();
}

class _SelectedContactState extends State<SelectedContact> {
  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;
  late ContactsCubit _contactsCubit;
  late SelectedContactCubit _selectedContactCubit;
  late InfoCubit _infoCubit;
  late MamListCubit _mamListCubit;
  late LoginCubit _loginCubit;
  late ChatRecentCubit _chatRecentCubit;

  late HiveService _hiveService;
  late Logger log;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);

    _hiveService = sl.get<HiveService>();
    log = sl.get<Logger>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: colorTheme.surfaceColor,
            leading: isDesktop
                ? IconButton(
                    splashRadius: context.deviceHeight(0.035),
                    onPressed: () {
                      _selectedContactCubit.removeSelectedContacInfo();
                    },
                    icon: Icon(
                      Icons.close,
                      color: colorTheme.primaryColor,
                    ),
                  )
                : null,
            title: const Center(
              child: Text('Contact Details'),
            ),
            centerTitle: true,
            actions: <Widget>[
              BlocBuilder<SelectedContactCubit, SelectedContactState>(
                builder: (context, selectedContactCubit) {
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                    child: IconButton(
                      onPressed: () {
                        _selectedContactCubit.addOrRemoveFavourite();
                      },
                      icon: Icon(
                        selectedContactCubit.isFavouriteContact ? Icons.favorite : Icons.favorite_border,
                        color: colorTheme.primaryColor,
                      ),
                    ),
                  );
                },
              ),
              BlocBuilder<SelectedContactCubit, SelectedContactState>(
                builder: (context, selectedContactState) {
                  return selectedContactState.isLocalContact && !selectedContactState.isPhoneBookContact
                      ? Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: IconButton(
                            splashRadius: context.deviceHeight(0.035),
                            onPressed: () {
                              // var localc = box.getAt();
                              showAddContactDialog(
                                context,
                                contactName: selectedContactState.displayName,
                                contactNumber: selectedContactState.contactId,
                                isEdit: true,
                              );
                            },
                            icon: Icon(
                              Icons.edit,
                              color: context.colorTheme().primary,
                            ),
                          ),
                        )
                      : const SizedBox.shrink();
                },
              ),
              BlocBuilder<SelectedContactCubit, SelectedContactState>(
                builder: (context, selectedContactState) {
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                    child: IconButton(
                      splashRadius: context.deviceHeight(0.035),
                      onPressed: () async {
                        final List<LocalContact> lcBox = _hiveService.getAllData<LocalContact>();

                        bool isLocalContact = false;

                        int index = 0;

                        if (lcBox.isNotEmpty) {
                          for (int i = 0; i < lcBox.length; i++) {
                            var boxData = lcBox[i];

                            if (selectedContactState.displayName == boxData.displayName &&
                                selectedContactState.contactId == boxData.contactNumber &&
                                selectedContactState.sipAddress == '-') {
                              isLocalContact = true;

                              index = i;

                              break;
                            }
                          }

                          if (isLocalContact) {
                            showConfirmDialog(
                              context: context,
                              title: 'Confirm delete contact?',
                              rightButtonText: 'Yes',
                              onPressedRightButton: () {
                                _hiveService.deleteDataByIndex<LocalContact>(
                                  index: index,
                                );

                                _hiveService.updateAllCallRecordsName(
                                  newName: selectedContactState.contactId,
                                  did: selectedContactState.contactId,
                                );

                                _selectedContactCubit.addOrRemoveFavourite(removeOnly: true);

                                _selectedContactCubit.removeSelectedContacInfo();
                              },
                              leftButtonText: 'No',
                            );
                          } else {
                            EasyLoading.showInfo('Not local contact, cannot delete');
                          }
                        } else {
                          EasyLoading.showInfo('Not local contact, cannot delete');
                        }
                      },
                      icon: Icon(
                        Icons.delete,
                        color: context.colorTheme().primary,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          body: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
              child: BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  final colorTheme = themeState.colorTheme;
                  final textTheme = themeState.themeData.textTheme;

                  return BlocBuilder<SelectedContactCubit, SelectedContactState>(
                    builder: (context, selectedContactState) {
                      final String displayName = selectedContactState.displayName;
                      final String contactId = selectedContactState.contactId;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          const Center(child: RoundShapeAvatar()),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(15, 0, 15, 15),
                            child: Center(
                              child: Text(
                                displayName,
                                style: context.responsiveSize<TextStyle>(
                                  moileSize: textTheme.displaySmall!.copyWith(color: colorTheme.primaryColor),
                                  tabletSize: textTheme.displaySmall!.copyWith(color: colorTheme.primaryColor),
                                  desktopSize: textTheme.displayMedium!.copyWith(color: colorTheme.primaryColor),
                                  largeScreenSize: textTheme.displayLarge!.copyWith(color: colorTheme.primaryColor),
                                ),
                                // style: textTheme.displayMedium!.copyWith(
                                //   color: colorTheme.primaryColor,
                                //   fontSize: context.isLargerOrEqualTo4kSize ? 50 : textTheme.displayMedium!.fontSize,
                                // ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                          // CommonCard(
                          //   title: 'Address',
                          //   desc: selectedContactState.sipAddress,
                          // ),
                          CommonCard(
                            title: 'Phone Number',
                            desc: contactId,
                          ),
                          Padding(
                            padding: EdgeInsets.all(context.deviceHeight(0.05)),
                            child: BlocBuilder<ThemeCubit, ThemeState>(
                              builder: (context, themeState) {
                                final colorTheme = themeState.colorTheme;

                                return Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    !selectedContactState.isLocalContact
                                        ? BlocBuilder<LoginCubit, LoginState>(
                                            builder: (context, loginState) {
                                              if (loginState is LoginAuthenticated) {
                                                return BlocBuilder<InfoCubit, InfoState>(
                                                  builder: (context, infoState) {
                                                    return RoundShapeInkWell(
                                                      onTap: () async {
                                                        popUntilInitial();
                                                        _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);
                                                        _chatRecentCubit.setPageIndex(kDirectMessagesIndex);
                                                        _infoCubit.getChatHistory(
                                                            receiver: selectedContactState.sipAddress,
                                                            loginCubit: _loginCubit,
                                                            mamListCubit: _mamListCubit);
                                                        if (isMobile) {
                                                          pushNamed(ChatPage.routeName);
                                                        }
                                                      },
                                                      contentWidget: const Icon(
                                                        Icons.message,
                                                        color: Colors.black,
                                                        size: iconSizeLarge,
                                                      ),
                                                      color: colorTheme.primaryColor,
                                                    );
                                                  },
                                                );
                                              } else {
                                                return const Center(
                                                  child: Text(
                                                    'Not Authenticated',
                                                    style: TextStyle(color: Colors.red, fontSize: 15),
                                                  ),
                                                );
                                              }
                                            },
                                          )
                                        : const SizedBox.shrink(),
                                    RoundShapeInkWell(
                                      onTap: () {
                                        _homeCubit.makeCall(
                                          receiverName: displayName,
                                          extNum: contactId,
                                          janusCubit: _janusCubit,
                                          contactsCubit: _contactsCubit,
                                          context: context,
                                        );
                                      },
                                      contentWidget: const Icon(
                                        Icons.call,
                                        color: Colors.black,
                                        size: iconSizeLarge,
                                      ),
                                      color: colorTheme.primaryColor,
                                    ),
                                  ],
                                );
                              },
                            ),
                          )
                        ],
                      );
                    },
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
