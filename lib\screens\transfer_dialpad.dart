import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/dialpad_bottom_buttons.dart';
import 'package:ddone/components/list_tile/card_list_tile_with_title.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/focus/focus_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/cubit/transfer_dialpad/transfer_dialpad_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TransferDialpad extends StatefulWidget {
  const TransferDialpad({super.key});

  @override
  State<TransferDialpad> createState() => _TransferDialpadState();
}

class _TransferDialpadState extends State<TransferDialpad> with AutomaticKeepAliveClientMixin<TransferDialpad> {
  @override
  // keep page alive
  bool get wantKeepAlive => true;

  //dialpad entered number initial value
  String? _dest;
  late SharedPreferences _preferences;
  late TextEditingController _textController;
  late TextEditingController _searchNameController;

  late final BoxCollection cdrBox;

  double height = 0;
  String searchValue = '';

  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;
  late ContactsCubit _contactsCubit;
  late TransferDialpadCubit _transferDialpadCubit;
  late FocusCubit _focusCubit;

  void loadSettings() async {
    _preferences = sl.get<SharedPreferences>();

    _dest = _preferences.getString('dest') ?? '';
    _textController = TextEditingController(text: _dest);
    _textController.text = _dest!;
  }

  void _handleNum(String number) {
    _textController.text += number;
  }

  void _handleBackSpace([bool deleteAll = false]) {
    String text = _textController.text;

    if (text.isNotEmpty) {
      text = deleteAll ? '' : text.substring(0, text.length - 1);

      _textController.text = text;
    }
  }

  @override
  initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _focusCubit = BlocProvider.of<FocusCubit>(context);

    _transferDialpadCubit = TransferDialpadCubit.initial();

    _transferDialpadCubit.getContacts(_contactsCubit);

    _searchNameController = TextEditingController();
    _textController = TextEditingController();

    loadSettings();
  }

  @override
  void dispose() async {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Row(
      children: [
        if (!isMobile)
          SizedBox(
            width: context.deviceWidth(0.4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 0, 20),
                    child: CommonTextField(
                      textEditingController: _searchNameController,
                      onValueChanged: (value) {
                        _transferDialpadCubit.filterContactList(value);
                      },
                      onFocusChange: (value) {
                        if (value) {
                          _focusCubit.focusTransferContactField();
                        } else {
                          _focusCubit.unfocusTransferContactField();
                        }
                      },
                      hintText: 'Search',
                      prefixIcon: const Icon(
                        Icons.search,
                        size: iconSizeMedium,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: context.deviceHeight(0.73),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(25, 0, 25, 15),
                    child: ScrollbarTheme(
                      data: ScrollbarThemeData(thumbColor: WidgetStatePropertyAll(Colors.grey.shade700)),
                      child: BlocBuilder<TransferDialpadCubit, TransferDialpadState>(
                        bloc: _transferDialpadCubit,
                        builder: (context, transferDialpadState) {
                          final filteredContactModelList = transferDialpadState.filteredContactModelList;

                          return ListView.builder(
                            itemCount: filteredContactModelList.length,
                            itemBuilder: (context, index) {
                              final filteredContact = filteredContactModelList[index];
                              final displayName = filteredContact.displayName;

                              bool showTitle = true;

                              if (index != 0) {
                                showTitle = !filteredContact.displayName
                                    .toLowerCase()
                                    .startsWith(filteredContactModelList[index - 1].displayName[0].toLowerCase());
                              }

                              return BlocBuilder<ThemeCubit, ThemeState>(
                                builder: (context, themeState) {
                                  final colorTheme = themeState.colorTheme;

                                  return CardListTileWithTitle(
                                    title: displayName[0].toUpperCase(),
                                    listTileTitle: displayName,
                                    leadingIcon: Icon(
                                      Icons.person_outline,
                                      color: colorTheme.primaryColor,
                                    ),
                                    onTap: () async {
                                      // textfieldFocused = true;
                                      // _focusCubit.focusNode(_focusCubit.transferDialpadNode);

                                      await showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context2) {
                                          return AlertDialog(
                                            shape: const RoundedRectangleBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(32.0))),
                                            insetPadding: const EdgeInsets.all(10),
                                            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
                                            content: SizedBox(
                                              height: context.deviceHeight(0.17),
                                              width: context.deviceWidth(0.2),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Center(
                                                    child: Text(
                                                      'Call $displayName',
                                                      style: TextStyle(
                                                          color: context.colorTheme().primary,
                                                          fontSize: displayName.length > 15 ? 14 : 20),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: context.deviceHeight(0.03),
                                                  ),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      InkWell(
                                                        onTap: pop,
                                                        child: SizedBox(
                                                          width: context.deviceWidth(0.08),
                                                          height: context.deviceHeight(0.07),
                                                          child: const Align(
                                                              alignment: Alignment.center,
                                                              child: Text(
                                                                'No',
                                                                style: TextStyle(fontSize: 20, color: Colors.red),
                                                              )),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: context.deviceWidth(0.03),
                                                      ),
                                                      InkWell(
                                                        onTap: () async {
                                                          _homeCubit.transferCall(
                                                            extNum: filteredContact.contactId,
                                                            janusCubit: _janusCubit,
                                                          );
                                                          _textController.clear();
                                                          pop();
                                                          pop();
                                                        },
                                                        child: SizedBox(
                                                          width: context.deviceWidth(0.08),
                                                          height: context.deviceHeight(0.07),
                                                          child: const Align(
                                                            alignment: Alignment.center,
                                                            child: Text(
                                                              'Yes',
                                                              style: TextStyle(
                                                                fontSize: 20,
                                                                color: Colors.green,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    },
                                    showTitle: showTitle,
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        if (!isMobile)
          SizedBox(
            width: context.deviceWidth(0.003),
            height: context.deviceHeight(0.88),
            child: Container(
              color: Colors.black,
            ),
          ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  final colorTheme = themeState.colorTheme;
                  final textTheme = themeState.themeData.textTheme;

                  return CommonTextField(
                    onFocusChange: (value) {
                      if (value) {
                        _focusCubit.focusTransferDialpadField();
                      } else {
                        _focusCubit.unfocusTransferDialpadField();
                      }
                    },
                    textEditingController: _textController,
                    textFieldHeight: heightLarge,
                    textAlign: TextAlign.center,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    textStyle: textTheme.displayLarge!.copyWith(color: colorTheme.onPrimaryColor),
                  );
                },
              ),
              SizedBox(
                height: context.deviceHeight(0.05),
              ),
              Numpad(
                callBack: (value) {
                  _handleNum(value);
                },
              ),
              DialpadBottomButtons(
                onLeftButtonClick: pop,
                onMiddleButtonClick: () {
                  if (_textController.text != '') {
                    _homeCubit.transferCall(
                      extNum: _textController.text,
                      janusCubit: _janusCubit,
                    );
                    _textController.clear();
                    pop();
                  }
                },
                onRightButtonClick: () {
                  _handleBackSpace();
                },
                onRightButtonLongPress: () {
                  _handleBackSpace(true);
                },
                leftIcon: Icons.close,
                middleIcon: Icons.phone_forwarded,
                rightIcon: Icons.backspace_outlined,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
