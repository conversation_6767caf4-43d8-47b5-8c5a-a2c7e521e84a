enum JanusSipEvent {
  registered,
  unregistered,
  transferCall,
  hangup,
  ringing,
  proceeding,
  calling,
  progress,
  accepted,
  incomingCall,
}

extension JanusSipEventExtension on JanusSipEvent {
  String statusMessage() {
    const String callConnected = 'Call Connected!';

    switch (this) {
      case JanusSipEvent.accepted:
        return callConnected;
      case JanusSipEvent.progress:
        return callConnected;
      case JanusSipEvent.calling:
        return 'Calling...';
      case JanusSipEvent.proceeding:
        return 'Proceeding...';
      case JanusSipEvent.hangup:
        return 'Call Ended!';
      default:
        return '';
    }
  }
}
