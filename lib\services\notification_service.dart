import 'dart:async';
import 'dart:math';

import 'package:ddone/service_locator.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/utils/notification_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:windows_notification/notification_message.dart';

const androidNotificationHighChannelId = 'high_importance_channel';
const androidNotificationHighChannelName = 'High Importance Notifications';
const androidNotificationHighChannelDesc = 'This channel is used for important notifications.';
const androidNotificationHighChannelImportance = Importance.high;
const androidNotificationMaxChannelImportance = Importance.max;
const int int32MaxValue = 2147483647;

class NotificationService {
  late final FlutterLocalNotificationsPlugin localNotificationsPlugin;
  late final CallkitService callkitService;
  late final SharedPreferencesService prefs;

  NotificationService()
      : localNotificationsPlugin = sl.get<FlutterLocalNotificationsPlugin>(),
        callkitService = sl.get<CallkitService>(),
        prefs = sl.get<SharedPreferencesService>();

  String? _activeChatId;
  final String _notificationIdPrefix = 'notfid_';

  Future<void> initializeLocalNotifications({
    Function(NotificationResponse)? onSelectNotification,
    Function(NotificationResponse)? onBackgroundSelectNotification,
  }) async {
    await localNotificationsPlugin.initialize(
      InitializationSettings(
        android: const AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(
          notificationCategories: iosMacosNotificationTemplate,
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
        ),
        macOS: DarwinInitializationSettings(
          notificationCategories: iosMacosNotificationTemplate,
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
        ),
      ),
      onDidReceiveNotificationResponse: onSelectNotification,
      onDidReceiveBackgroundNotificationResponse: onBackgroundSelectNotification,
    );

    // This channel is created to enable foreground notifications for Android
    await localNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          const AndroidNotificationChannel(
            androidNotificationHighChannelId,
            androidNotificationHighChannelName,
            description: androidNotificationHighChannelDesc,
            importance: androidNotificationMaxChannelImportance,
          ),
        );
  }

  Future<void> showLocalNotification({
    int? notificationId,
    required String title,
    required String desc,
    required String payload,
    AndroidNotificationDetails? androidNotificationDetails,
    List<AndroidNotificationAction>? androidNotificaitonAction,
    DarwinNotificationDetails? darwinNotificationDetails,
    NotificationMessage? windowNotificationMessage,
    String? windowsNotificationTemplate,
  }) async {
    if (isWindows) {
      // final windowsLocalNotification = sl.get<WindowsNotification>();

      // NotificationMessage notificationMessage = NotificationMessage.fromPluginTemplate(
      //   "test1",
      //   title,
      //   message,
      //   largeImage: '',
      //   image: '',
      // );

      // windowsLocalNotification.showNotificationPluginTemplate(notificationMessage);
      // windowsLocalNotification.showNotificationCustomTemplate(
      //   windowNotificationMessage ??
      //       NotificationMessage.fromPluginTemplate(
      //         '0',
      //         'title',
      //         'body',
      //       ),
      //   windowsNotificationTemplate ?? '',
      // );

      return;
    }

    await localNotificationsPlugin.show(
      notificationId ?? Random().nextInt(int32MaxValue),
      title,
      desc,
      NotificationDetails(
        android: androidNotificationDetails ??
            AndroidNotificationDetails(
              androidNotificationHighChannelId,
              androidNotificationHighChannelName,
              channelDescription: androidNotificationHighChannelDesc,
              visibility: NotificationVisibility.public,
              priority: Priority.max,
              importance: Importance.max,
              actions: androidNotificaitonAction,
            ),
        iOS: darwinNotificationDetails,
        macOS: darwinNotificationDetails,
      ),
      payload: payload,
    );
  }

  Future<NotificationAppLaunchDetails?> getNotificationAppLaunchDetails() async {
    return await localNotificationsPlugin.getNotificationAppLaunchDetails();
  }

  Future<bool> requestNotificationPermission() async {
    bool result = false;

    if (isIOS) {
      result = await localNotificationsPlugin
              .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
              ?.requestPermissions(
                alert: true,
                badge: true,
                sound: true,
                provisional: true,
              ) ??
          false;
    } else if (isMacOS) {
      result = await localNotificationsPlugin
              .resolvePlatformSpecificImplementation<MacOSFlutterLocalNotificationsPlugin>()
              ?.requestPermissions(
                alert: true,
                badge: true,
                sound: true,
                provisional:
                    true, //This type of permission system allows for notification permission to be instantly granted without displaying a dialog to your user
              ) ??
          false;
    }

    return result;
  }

  void setActiveChat(String chatId) {
    _activeChatId = chatId;
  }

  void removeActiveChat() {
    _activeChatId = null;
  }

  bool isChatActive(String chatId) {
    return _activeChatId == chatId;
  }

  bool hasProcessedNotification(String id) {
    String? n = prefs.getString(_notificationIdPrefix + id);
    return n != null;
  }

  void saveNotificationId(String id) async {
    await prefs.setString(_notificationIdPrefix + id, id, ttlInSeconds: const Duration(days: 1).inSeconds);
  }
}
