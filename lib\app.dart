import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/screens/home.dart';
import 'package:ddone/constants/keys/widget_keys.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/cubits.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/cubit/bloc_observer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class DDOneApp extends StatefulWidget {
  const DDOneApp({super.key});

  @override
  State<DDOneApp> createState() => _MyAppState();
}

class _MyAppState extends State<DDOneApp> {
  late AppRouter _appRouter;

  @override
  void initState() {
    super.initState();

    _appRouter = AppRouter();
  }

  @override
  Widget build(BuildContext context) {
    Bloc.observer = AppBlocObserver();

    return MultiBlocProvider(
      providers: globalBlocProviders,
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
            child: MaterialApp(
              navigatorObservers: [SentryNavigatorObserver()],
              navigatorKey: WidgetKeys.navKey,
              themeMode: ThemeMode.system,
              theme: themeState.themeData,
              debugShowCheckedModeBanner: false,
              onGenerateRoute: _appRouter.onGenerateRoute,
              home: _flavorBanner(show: kDebugMode),
              builder: (context, child) {
                return EasyLoading.init()(
                  context,
                  ResponsiveBreakpoints.builder(
                    breakpoints: [
                      const Breakpoint(start: 0, end: 450, name: MOBILE),
                      const Breakpoint(start: 451, end: 800, name: TABLET),
                      const Breakpoint(start: 801, end: 1920, name: DESKTOP),
                      const Breakpoint(start: 1921, end: double.infinity, name: largeScreen),
                    ],
                    child: child!,
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _flavorBanner({
    bool show = true,
  }) =>
      show
          ? Banner(
              location: BannerLocation.topStart,
              message: env!.flavor.name,
              color: Colors.green.withOpacity(0.6),
              textStyle: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 12.0,
                letterSpacing: 1.0,
              ),
              textDirection: TextDirection.ltr,
              child: const Home(),
            )
          : const Home();
}
