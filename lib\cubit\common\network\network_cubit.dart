import 'dart:async';
import 'dart:ui';
import 'package:bloc/bloc.dart';
import 'package:ddone/events/app_state_event.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/service_locator.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:event_bus_plus/event_bus_plus.dart';

part 'network_state.dart';

class NetworkCubit extends Cubit<NetworkState> {
  final Connectivity _connectivity;
  final IEventBus _eventBus;
  late final StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  AppLifecycleState? _appState;

  NetworkCubit._({NetworkState? state})
      : _connectivity = sl.get<Connectivity>(),
        _eventBus = sl.get<IEventBus>(),
        super(const NetworkInitital()) {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateNetworkState);
    _eventBus.on<AppStateEvent>().listen((event) {
      _appState = event.state;
      if (event.state == AppLifecycleState.resumed) {
        _networkConnected();
      }
    });
  }

  factory NetworkCubit.initial({NetworkState? state}) {
    if (sl.isRegistered<NetworkCubit>()) {
      return sl.get<NetworkCubit>();
    }

    return NetworkCubit._(state: state);
  }

  void _updateNetworkState(List<ConnectivityResult> result) {
    if (result.first == ConnectivityResult.none) {
      emit(const NetworkDisconnected());
    } else {
      if (state is NetworkInitital) {
        emit(const NetworkConnected());
      } else {
        emit(const NetworkReconnected());
        // we have a separate eventbus for network event because bloc cubit rely on widget tree,
        // only when widget need to be updated, state will get propagated.
        // However, this is not sufficient for network connection because we need to actively maintain
        // the connection even when widget is in background (does not need to be udpated).
        _eventBus.fire(const NetworkEvent(NetworkReconnected()));
        if (_appState == AppLifecycleState.resumed) {
          _networkConnected();
        }
      }
    }
  }

  void _networkConnected() async {
    if (state is NetworkReconnected) {
      await Future.delayed(const Duration(seconds: 3)); // let NetworkReconnected stay for 3 sec
      emit(const NetworkConnected()); // reset it back to NetworkConnected
    }
  }

  @override
  Future<void> close() {
    _connectivitySubscription.cancel();
    return super.close();
  }
}
