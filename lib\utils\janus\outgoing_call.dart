import 'package:avatar_glow/avatar_glow.dart';
import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/widgets/audio_device_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class OutGoingCallDialog extends StatefulWidget {
  final StopWatchTimer stopWatchTimer;

  OutGoingCallDialog({super.key}) : stopWatchTimer = sl.get<StopWatchTimer>();

  @override
  State<OutGoingCallDialog> createState() => _OutGoingCallDialogState();
}

class _OutGoingCallDialogState extends State<OutGoingCallDialog> {
  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;

  late TextEditingController _numberController;

  late ScrollController _scrollController;

  String displayTime = '0:00';

  bool showNumpad = false;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);

    _numberController = TextEditingController();

    _scrollController = ScrollController();

    _numberController.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  @override
  void dispose() {
    _numberController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<JanusCubit, JanusState>(
      listener: (context, janusState) {
        if (janusState is JanusSipHangupEvent) {
          popUntilInitial();
        } else if (janusState is JanusSipHangupErrorEvent) {
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          return AlertDialog(
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(32.0))),
            insetPadding: const EdgeInsets.all(10),
            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
            content: SizedBox(
              width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
              child: BlocBuilder<JanusCubit, JanusState>(
                builder: (context, janusState) {
                  final statusMessage = janusState.statusMessage;

                  return Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      showNumpad
                          ? const SizedBox.shrink()
                          : AvatarGlow(
                              glowColor: const Color.fromARGB(255, 109, 109, 109),
                              child: Material(
                                // Replace this child with your own
                                elevation: 3.0,
                                shape: const CircleBorder(),
                                child: CircleAvatar(
                                  radius: context.responsiveSize<double>(
                                    moileSize: 70,
                                    tabletSize: 70,
                                    desktopSize: context.deviceWidth(0.03),
                                    largeScreenSize: context.deviceWidth(0.02),
                                  ),
                                  backgroundColor: const Color.fromARGB(255, 83, 83, 83),
                                  child: Icon(
                                    Icons.call,
                                    color: Colors.orange,
                                    size: context.responsiveSize<double>(
                                      moileSize: 70,
                                      tabletSize: 70,
                                      desktopSize: context.deviceWidth(0.03),
                                      largeScreenSize: context.deviceWidth(0.02),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                      Column(
                        children: [
                          Text(
                            janusState.callTo,
                            style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                          ),
                          SizedBox(height: context.deviceHeight(0.02)),
                          Text(
                            statusMessage,
                            style: const TextStyle(color: Colors.orange),
                          ),
                          SizedBox(height: context.deviceHeight(0.03)),
                          janusState is JanusSipAcceptedEvent && !showNumpad
                              ? StreamBuilder<int>(
                                  stream: widget.stopWatchTimer.rawTime,
                                  initialData: 0,
                                  builder: (context, snap) {
                                    final value = snap.data;

                                    displayTime = StopWatchTimer.getDisplayTime(value!);

                                    return Column(
                                      children: <Widget>[
                                        Padding(
                                          padding: const EdgeInsets.all(8),
                                          child: Text(
                                            displayTime,
                                            style: const TextStyle(
                                              fontSize: 15,
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                      if (janusState is JanusSipAcceptedEvent)
                        showNumpad
                            ? Column(
                                children: [
                                  BlocBuilder<ThemeCubit, ThemeState>(
                                    builder: (context, themeState) {
                                      final colorTheme = themeState.colorTheme;
                                      final textTheme = themeState.themeData.textTheme;

                                      return CommonTextField(
                                        // onFocusChange: (value) {
                                        //   if (value) {
                                        //     _focusCubit.focusDialpadField();
                                        //   } else {
                                        //     _focusCubit.unfocusDialpadField();
                                        //   }
                                        // },
                                        // focusNode: _focusCubit.dialpadNode,
                                        readOnly: true,
                                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                                        textEditingController: _numberController,
                                        scrollController: _scrollController,
                                        textFieldHeight: heightLarge,
                                        textAlign: TextAlign.center,
                                        textStyle: textTheme.displayLarge!.copyWith(color: colorTheme.onPrimaryColor),
                                      );
                                    },
                                  ),
                                  const SizedBox(height: spacingExtraLarge),
                                  Numpad(
                                    callBack: (value) {
                                      setState(() {
                                        _numberController.text += value;
                                      });

                                      _homeCubit.sendDTMF(value);
                                    },
                                  ),
                                ],
                              )
                            : Column(
                                children: [
                                  Wrap(
                                    alignment: WrapAlignment.center,
                                    children: [
                                      RoundShapeInkWell(
                                        onTap: () {
                                          showTransferDialpadDialog(context);
                                        },
                                        color: colorTheme.primaryColor,
                                        contentWidget: const Icon(Icons.phone_forwarded),
                                      ),
                                      BlocBuilder<HomeCubit, HomeState>(
                                        builder: (context, homeState) {
                                          return RoundShapeInkWell(
                                            onTap: () {
                                              _homeCubit.toggleMic(janusCubit: _janusCubit);
                                            },
                                            color: colorTheme.primaryColor,
                                            contentWidget:
                                                homeState.isMicOn ? const Icon(Icons.mic) : const Icon(Icons.mic_off),
                                          );
                                        },
                                      ),
                                      RoundShapeInkWell(
                                        color: colorTheme.primaryColor,
                                        contentWidget: AudioDeviceSelector(
                                          janusCubit: _janusCubit,
                                          colorTheme: colorTheme,
                                        ),
                                      ),
                                      RoundShapeInkWell(
                                        onTap: () async {
                                          await _homeCubit.hold(janusCubit: _janusCubit);
                                          if (context.mounted) {
                                            showOnholdCallDialog(
                                              context,
                                              onTap: () async {
                                                await _homeCubit.unhold(janusCubit: _janusCubit);
                                                pop();
                                              },
                                            );
                                          }
                                        },
                                        color: colorTheme.primaryColor,
                                        contentWidget: const Icon(Icons.pause),
                                      ),
                                    ],
                                  ),
                                  RoundShapeInkWell(
                                    onTap: () {
                                      setState(() {
                                        showNumpad = true;
                                      });
                                    },
                                    color: colorTheme.primaryColor,
                                    contentWidget: const Icon(Icons.dialpad),
                                  ),
                                ],
                              ),
                      SizedBox(height: context.deviceWidth(0.03)),
                      Row(
                        children: [
                          const Spacer(),
                          Padding(
                            padding: const EdgeInsets.all(0.0),
                            child: RoundShapeInkWell(
                              onTap: () {
                                // print('_janusCubit.state: ${_janusCubit.state}');
                                // if (_janusCubit.state is JanusSipHangupEvent ||
                                //     _janusCubit.state is JanusSipHangupErrorEvent) {
                                //   pop();

                                //   return;
                                // }
                                popUntilInitial();

                                _homeCubit.hangup(janusCubit: _janusCubit);
                              },
                              checkNetwork: false,
                              color: colorTheme.errorColor,
                              contentWidget: const Icon(Icons.call_end),
                            ),
                          ),
                          Expanded(
                            child: showNumpad
                                ? Align(
                                    alignment: Alignment.centerLeft,
                                    child: TextButton(
                                        onPressed: () {
                                          setState(() {
                                            showNumpad = false;
                                          });
                                        },
                                        child: const Text('Hide Numpad')),
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

// class OnHoldDialog extends StatelessWidget {
//   final HomeCubit homeCubit;

//   const OnHoldDialog({
//     required this.homeCubit,
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(32.0))),
//       insetPadding: const EdgeInsets.all(10),
//       content: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         mainAxisSize: MainAxisSize.max,
//         children: [
//           const Text(
//             "Call on hold. Press the button below to unhold.",
//             style: TextStyle(
//               color: Colors.orange,
//             ),
//           ),
//           const SizedBox(height: 50),
//           CircleAvatar(
//             backgroundColor: Colors.orange,
//             radius: 30,
//             child: IconButton(
//               icon: const Icon(Icons.play_arrow),
//               color: colorTheme.onPrimaryColor,
//               onPressed: () async {
//                 await homeCubit.unhold();

//                 pop();
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
