import 'package:ddone/models/enums/platform_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/multiplatform/multiplatform.dart';

bool get isAndroid {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.android == platform;
}

bool get isIOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.ios == platform;
}

bool get isMobile {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.android,
    PlatformEnum.ios,
  }.contains(platform);
}

bool get isMacOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.macos == platform;
}

bool get isWindows {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.windows == platform;
}

bool get isDesktop {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.web,
    PlatformEnum.macos,
    PlatformEnum.windows,
    PlatformEnum.linux,
  }.contains(platform);
}
