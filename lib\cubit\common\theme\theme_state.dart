part of 'theme_cubit.dart';

enum ThemeType {
  defaultTheme,
  darkTheme,
}

abstract class ThemeState extends Equatable {
  final ThemeType themeType;
  final ColorTheme colorTheme;
  final ThemeData themeData;
  final LanguageOption language;

  const ThemeState({
    required this.themeType,
    required this.colorTheme,
    required this.themeData,
    required this.language,
  });

  factory ThemeState.initial({required ThemeType? themeType, required LanguageOption language}) {
    switch (themeType) {
      case ThemeType.darkTheme:
        return DarkThemeState.initial(language);
      default:
        return DefaultThemeState.initial(language);
    }
  }

  ThemeState changeLanguage(LanguageOption language);

  @override
  List<Object> get props => [themeType, language];
}

class DefaultThemeState extends ThemeState {
  const DefaultThemeState._({
    required super.colorTheme,
    required super.themeData,
    required super.language,
  }) : super(
          themeType: ThemeType.defaultTheme,
        );

  factory DefaultThemeState.initial(LanguageOption language) {
    const colorTheme = ColorTheme(
      primaryColor: Color(0xFFffb000),
      primaryVariantColor: Color(0xFFcc8c00),
      secondaryColor: Color(0xFFffb000),
      secondaryVariantColor: Color(0xFFcc8c00),
      backgroundColor: Color.fromARGB(255, 34, 34, 34),
      secondaryBackgroundColor: Color(0xFFBCE0FD),
      onSecondaryBackgroundColor: Colors.white,
      connectedColor: Colors.green,
      surfaceColor: Color.fromARGB(255, 34, 34, 34),
      onPrimaryColor: Colors.white,
      onSecondaryColor: Colors.white,
      onSurfaceColor: Colors.white,
      onBackgroundColor: Colors.white,
      errorColor: Colors.red,
      errorContainer: Color(0xFFFC100D),
      onErrorColor: Colors.white,
      roundShapeInkWellColor: Colors.black,
    );

    // final textTheme = _createTextThemeFromTemplate(colorTheme);

    final themeData = _createThemeDataFromTemplate(
      colorTheme: colorTheme,
      // textTheme: textTheme,
    );

    return DefaultThemeState._(
      colorTheme: colorTheme,
      themeData: themeData,
      language: language,
    );
  }

  @override
  List<Object> get props => super.props..addAll([]);

  @override
  ThemeState changeLanguage(LanguageOption language) {
    return DefaultThemeState._(
      colorTheme: colorTheme,
      themeData: themeData,
      language: language,
    );
  }
}

class DarkThemeState extends ThemeState {
  const DarkThemeState._({
    required super.colorTheme,
    required super.themeData,
    required super.language,
  }) : super(
          themeType: ThemeType.darkTheme,
        );

  factory DarkThemeState.initial(LanguageOption language) {
    const colorTheme = ColorTheme(
        primaryColor: Color(0xFFffb000),
        primaryVariantColor: Color(0xFFcc8c00),
        secondaryColor: Color(0xFFffb000),
        secondaryVariantColor: Color(0xFFcc8c00),
        backgroundColor: Color.fromARGB(255, 34, 34, 34),
        secondaryBackgroundColor: Color(0xFFBCE0FD),
        onSecondaryBackgroundColor: Colors.white,
        connectedColor: Colors.green,
        surfaceColor: Color.fromARGB(255, 34, 34, 34),
        onPrimaryColor: Colors.black,
        onSecondaryColor: Colors.black,
        onSurfaceColor: Colors.white,
        onBackgroundColor: Colors.white,
        errorColor: Colors.red,
        errorContainer: Color(0xFFFC100D),
        onErrorColor: Colors.white,
        roundShapeInkWellColor: Colors.black);

    // final textTheme = _createTextThemeFromTemplate(colorTheme);

    final themeData = _createThemeDataFromTemplate(
      colorTheme: colorTheme,
      brightness: Brightness.dark,
      // textTheme: textTheme,
    );

    return DarkThemeState._(
      colorTheme: colorTheme,
      themeData: themeData,
      language: language,
    );
  }

  @override
  List<Object> get props => super.props..addAll([]);

  @override
  ThemeState changeLanguage(LanguageOption language) {
    return DarkThemeState._(
      colorTheme: colorTheme,
      themeData: themeData,
      language: language,
    );
  }
}

///when define new color then add in here
///so we ensure every theme have same color attribute
class ColorTheme {
  final Color primaryColor, onPrimaryColor;
  final Color primaryVariantColor;
  final Color secondaryColor, onSecondaryColor;
  final Color secondaryVariantColor;
  final Color backgroundColor, onBackgroundColor;
  final Color secondaryBackgroundColor, onSecondaryBackgroundColor;
  final Color surfaceColor, onSurfaceColor;
  final Color errorColor, onErrorColor;
  final Color connectedColor;
  final Color errorContainer;
  final Color roundShapeInkWellColor;

  const ColorTheme({
    required this.primaryColor,
    required this.primaryVariantColor,
    required this.secondaryColor,
    required this.secondaryVariantColor,
    required this.surfaceColor,
    required this.backgroundColor,
    required this.secondaryBackgroundColor,
    required this.onSecondaryBackgroundColor,
    required this.connectedColor,
    required this.errorColor,
    required this.onPrimaryColor,
    required this.onSecondaryColor,
    required this.onSurfaceColor,
    required this.onBackgroundColor,
    required this.onErrorColor,
    required this.errorContainer,
    required this.roundShapeInkWellColor,
  });

  ColorScheme toColorScheme({
    Brightness brightness = Brightness.light,
  }) {
    return ColorScheme(
      primary: primaryColor,
      primaryContainer: primaryVariantColor,
      secondary: secondaryColor,
      secondaryContainer: secondaryVariantColor,
      surface: surfaceColor,
      error: errorColor,
      onPrimary: onPrimaryColor,
      onSecondary: onSecondaryColor,
      onSurface: onSurfaceColor,
      onError: onErrorColor,
      brightness: brightness,
      errorContainer: errorContainer,
    );
  }
}

// TextTheme _createTextThemeFromTemplate(
//   ColorTheme colorTheme, {
//   TextStyle? displaySmall,
//   TextStyle? headlineMedium,
//   TextStyle? headlineSmall,
//   TextStyle? titleLarge,
//   TextStyle? titleMedium,
//   TextStyle? titleSmall,
//   TextStyle? bodyLarge,
//   TextStyle? bodyMedium,
//   TextStyle? button,
//   TextStyle? bodySmall,
//   TextStyle? labelSmall,
// }) {
//   return TextTheme(
//     displaySmall: displaySmall ??
//         TextStyle(
//           fontSize: displaySmallFontSize,
//           fontWeight: displaySmallFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     headlineMedium: headlineMedium ??
//         TextStyle(
//           fontSize: headlineMediumFontSize,
//           fontWeight: headlineMediumFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     headlineSmall: headlineSmall ??
//         TextStyle(
//           fontSize: headlineSmallFontSize,
//           fontWeight: headlineSmallFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     titleLarge: titleLarge ??
//         TextStyle(
//           fontSize: titleLargeFontSize,
//           fontWeight: titleLargeFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     titleMedium: titleMedium ??
//         TextStyle(
//           fontSize: titleMediumFontSize,
//           fontWeight: titleMediumFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     titleSmall: titleSmall ??
//         TextStyle(
//           fontSize: titleSmallFontSize,
//           fontWeight: titleSmallFontWeight,
//           color: colorTheme.onBackgroundColor.withOpacity(opacityMed),
//         ),
//     bodyLarge: bodyLarge ??
//         TextStyle(
//           fontSize: bodyLargeFontSize,
//           fontWeight: bodyLargeFontWeight,
//           color: colorTheme.onBackgroundColor,
//         ),
//     bodyMedium: bodyMedium ??
//         TextStyle(
//           fontSize: bodyMediumFontSize,
//           fontWeight: bodyMediumFontWeight,
//           color: colorTheme.onBackgroundColor.withOpacity(opacityMed),
//         ),
//     labelLarge: button ??
//         TextStyle(
//           fontSize: labelLargeFontSize,
//           fontWeight: labelLargeFontWeight,
//           color: colorTheme.onPrimaryColor,
//         ),
//     bodySmall: bodySmall ??
//         TextStyle(
//           fontSize: bodySmallFontSize,
//           fontWeight: bodySmallFontWeight,
//           color: colorTheme.onBackgroundColor.withOpacity(opacityMed),
//         ),
//     labelSmall: labelSmall ??
//         TextStyle(
//           fontSize: labelSmallFontSize,
//           fontWeight: labelSmallFontWeight,
//           color: colorTheme.onBackgroundColor.withOpacity(opacityMed),
//         ),
//   );
// }

ThemeData _createThemeDataFromTemplate({
  required ColorTheme colorTheme,
  Brightness brightness = Brightness.light,
}) {
  final colorScheme = colorTheme.toColorScheme(brightness: brightness);

  final backgroundColor = colorTheme.backgroundColor;
  final onBackgroundColor = colorTheme.onBackgroundColor;
  final primaryColor = colorTheme.primaryColor;

  const white70 = Colors.white70;

  return ThemeData(
    brightness: brightness,
    colorScheme: colorScheme,
    primaryColor: primaryColor,
    scaffoldBackgroundColor: backgroundColor,
    appBarTheme: AppBarTheme(
      color: primaryColor,
      iconTheme: IconThemeData(color: onBackgroundColor),
      titleTextStyle: TextStyle(color: primaryColor, fontSize: 20),
      surfaceTintColor: Colors.transparent,
    ),
    buttonTheme: ButtonThemeData(
      buttonColor: primaryColor,
      textTheme: ButtonTextTheme.primary,
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: primaryColor,
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: primaryColor,
      selectedItemColor: backgroundColor,
      unselectedItemColor: backgroundColor,
      showUnselectedLabels: true,
    ),
    bottomAppBarTheme: BottomAppBarTheme(
      color: backgroundColor,
    ),
    textTheme: TextTheme(
      displayLarge: TextStyle(color: onBackgroundColor, fontSize: displayLargeFontSize), //34
      displayMedium: TextStyle(color: onBackgroundColor, fontSize: displayMediumFontSize), //24
      displaySmall: TextStyle(color: onBackgroundColor, fontSize: displaySmallFontSize), //20
      headlineLarge: TextStyle(color: onBackgroundColor, fontSize: headlineLargeFontSize), //32
      headlineMedium: TextStyle(color: onBackgroundColor, fontSize: headlineMediumFontSize), //28
      headlineSmall: TextStyle(color: onBackgroundColor, fontSize: headlineSmallFontSize), //24
      titleLarge: TextStyle(color: onBackgroundColor, fontSize: titleLargeFontSize), //22
      titleMedium: TextStyle(color: onBackgroundColor, fontSize: titleMediumFontSize), //20
      titleSmall: TextStyle(color: onBackgroundColor, fontSize: titleSmallFontSize), //18
      bodyLarge: TextStyle(color: onBackgroundColor, fontSize: bodyLargeFontSize), //16
      bodyMedium: TextStyle(color: onBackgroundColor, fontSize: bodyMediumFontSize), //14
      bodySmall: TextStyle(color: onBackgroundColor, fontSize: bodySmallFontSize), //12
      labelLarge: TextStyle(color: onBackgroundColor, fontSize: labelLargeFontSize), //14
      labelMedium: TextStyle(color: onBackgroundColor, fontSize: labelMediumFontSize), //12
      labelSmall: const TextStyle(color: white70, fontSize: labelSmallFontSize), //10
    ),
    cardColor: backgroundColor,
    iconTheme: IconThemeData(color: onBackgroundColor),
    inputDecorationTheme: InputDecorationTheme(
      filled: false,
      fillColor: backgroundColor,
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      focusedErrorBorder: InputBorder.none,
      labelStyle: TextStyle(color: onBackgroundColor),
      hintStyle: const TextStyle(color: white70),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: BorderSide(color: primaryColor),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.all(primaryColor),
      trackColor: WidgetStateProperty.all(primaryColor.withOpacity(0.5)),
    ),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.all(primaryColor),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.all(primaryColor),
    ),
    sliderTheme: SliderThemeData(
      activeTrackColor: primaryColor,
      thumbColor: primaryColor,
    ),
    navigationRailTheme: NavigationRailThemeData(
      selectedIconTheme: IconThemeData(color: onBackgroundColor),
      selectedLabelTextStyle: TextStyle(color: primaryColor),
    ),
  );
}
