import 'dart:async';
import 'dart:io';
import 'package:ddone/app.dart';
import 'package:ddone/constants/keys/widget_keys.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/init.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/sentry_util.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:launch_at_startup/launch_at_startup.dart';
import 'package:get_it/get_it.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_store_plus/media_store_plus.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:moxxmpp_socket_tcp/moxxmpp_socket_tcp.dart';
import 'package:screen_retriever/screen_retriever.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:window_manager/window_manager.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

Future<void> main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    MediaKit.ensureInitialized();

    await FirebaseService.initializeApp();

    await initializeLogger();

    await mainInit();

    if (isMobile) {
      MediaStore.ensureInitialized();
      MediaStore.appFolder = 'DDOne';
    }

    if (isDesktop) {
      launchAtStartup.setup(
        appName: 'DDOne',
        appPath: Platform.resolvedExecutable,
        // Set packageName parameter to support MSIX.
        packageName: 'com.dotdashtech.ddone',
      );
    }

    final BookmarkListCubit bookmarkListCubit = BookmarkListCubit();

    GetIt.I.registerSingleton<XmppConnection>(
      XmppConnection(
        RandomBackoffReconnectionPolicy(1, 60),
        AlwaysConnectedConnectivityManager(),
        ClientToServerNegotiator(),
        // The below causes the app to crash.
        //ExampleTcpSocketWrapper(),
        // In a production app, the below should be false.
        TCPSocketWrapper(false),
        bookmarkListCubit: bookmarkListCubit,
      ),
    );
    //fluttrt allow self sign cert - no good, remove after getting a valid cert
    HttpOverrides.global = MyHttpOverrides();

    EasyLoadingService().initEasyLoadingService();

    if (isDesktop) {
      Display primaryScreen = await screenRetriever.getPrimaryDisplay();
      double appWidth = primaryScreen.size.width * 0.6;
      double appHeight = primaryScreen.size.height * 0.8;

      await windowManager.ensureInitialized();

      WindowManager.instance.setSize(Size(appWidth, appHeight));
      WindowManager.instance.setResizable(true);
      WindowManager.instance.setMaximizable(true);
      WindowManager.instance.setMinimumSize(Size(appWidth, appHeight));
      // WindowManager.instance.setMaximumSize(Size(appWidth, appHeight));
    }

    await SentryFlutter.init(
      (options) {
        options
          ..dsn = env!.sentryUrl
          ..tracesSampleRate = 1.0
          ..profilesSampleRate = 1.0
          ..screenshotQuality = SentryScreenshotQuality.low
          ..navigatorKey = WidgetKeys.navKey;
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        // options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        // options.profilesSampleRate = 1.0;

        if (isDevMode()) {
          options
            ..debug = true
            ..transport = MockSentryTransport(); //this is use to disable sentry log
        }
      },
      appRunner: () => SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp, // Only portrait up
        // DeviceOrientation.portraitDown, // Uncomment to allow portrait down as well
      ]).then((_) {
        runApp(const DDOneApp());
      }),
    );
  }, (error, stackTrace) {
    log.f('runZonedGuarded', error: error, stackTrace: stackTrace);
    // Sentry.captureException(error, stackTrace: stackTrace);
  });
}
