part of 'janus_cubit.dart';

abstract class JanusState extends Equatable {
  final String statusMessage;
  final String callTo;

  const JanusState({
    this.statusMessage = '',
    this.callTo = '',
  });

  @override
  List<Object?> get props => [
        statusMessage,
        callTo,
      ];
}

class JanusInitial extends JanusState {
  const JanusInitial({statusMessage = ''}) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipRegisteredEvent extends JanusState {
  const JanusSipRegisteredEvent({
    super.statusMessage = '',
    super.callTo = '',
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipUnregisteredEvent extends JanusState {
  const JanusSipUnregisteredEvent({statusMessage = ''}) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipIncomingCallEvent extends JanusSipRegisteredEvent {
  final String? callerID;
  final String? caller;

  const JanusSipIncomingCallEvent({
    required statusMessage,
    required this.callerID,
    required this.caller,
  }) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props
    ..addAll([
      callerID,
      caller,
    ]);
}

class JanusSipAcceptedEvent extends JanusSipRegisteredEvent {
  final String? callerID;
  final String? caller;

  const JanusSipAcceptedEvent({
    required super.statusMessage,
    required this.callerID,
    required this.caller,
  });

  @override
  List<Object?> get props => super.props
    ..addAll([
      callerID,
      caller,
    ]);
}

class JanusSipAcceptErrorEvent extends JanusSipRegisteredEvent {
  const JanusSipAcceptErrorEvent();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipHangupErrorEvent extends JanusSipRegisteredEvent {
  const JanusSipHangupErrorEvent();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipProgressEvent extends JanusSipRegisteredEvent {
  const JanusSipProgressEvent({
    required super.statusMessage,
    required super.callTo,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipCallingEvent extends JanusSipRegisteredEvent {
  const JanusSipCallingEvent({
    required super.statusMessage,
    required super.callTo,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipTransferCallEvent extends JanusSipRegisteredEvent {
  const JanusSipTransferCallEvent({required statusMessage}) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipProceedingEvent extends JanusSipRegisteredEvent {
  const JanusSipProceedingEvent({
    required super.statusMessage,
    required super.callTo,
  });

  @override
  List<Object?> get props => super.props..addAll([callTo]);
}

class JanusSipRingingEvent extends JanusSipRegisteredEvent {
  const JanusSipRingingEvent({required statusMessage}) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusSipHangupEvent extends JanusState {
  const JanusSipHangupEvent({required statusMessage}) : super(statusMessage: statusMessage);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class JanusRegisterError extends JanusState {
  final String errorMsg;

  const JanusRegisterError({
    this.errorMsg = '',
  }) : super(statusMessage: '');

  @override
  List<Object?> get props => super.props..addAll([errorMsg]);
}

class JanusConnectionError extends JanusState {
  final String errorMsg;

  const JanusConnectionError({
    this.errorMsg = '',
  }) : super(statusMessage: '');

  @override
  List<Object?> get props => super.props..addAll([errorMsg]);
}
