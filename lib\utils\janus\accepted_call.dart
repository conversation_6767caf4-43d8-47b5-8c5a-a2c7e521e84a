import 'package:avatar_glow/avatar_glow.dart';
import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/service_locator.dart';
// import 'package:ddone/services/janus_backgroud_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/widgets/audio_device_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phone_state/phone_state.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class AcceptedCallDialog extends StatefulWidget {
  final StopWatchTimer stopWatchTimer;
  final String? caller, callerID;
  final void Function(String value) onDecline;

  AcceptedCallDialog({
    this.caller,
    this.callerID,
    required this.onDecline,
    super.key,
  }) : stopWatchTimer = sl.get<StopWatchTimer>();

  @override
  State<AcceptedCallDialog> createState() => _AcceptedCallDialogState();
}

class _AcceptedCallDialogState extends State<AcceptedCallDialog> {
  late HomeCubit _homeCubit;
  late JanusCubit _janusCubit;

  late TextEditingController _numberController;

  late ScrollController _scrollController;

  String _displayTime = '0:00';

  bool showNumpad = false;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _janusCubit = BlocProvider.of<JanusCubit>(context);

    _numberController = TextEditingController();

    _scrollController = ScrollController();

    _numberController.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  @override
  void dispose() {
    _numberController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<JanusCubit, JanusState>(
      listener: (context, janusState) {
        if (janusState is JanusSipHangupEvent) {
          widget.onDecline.call(_displayTime);

          popUntilInitial();
        } else if (janusState is JanusSipHangupErrorEvent) {
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;
          return StreamBuilder<PhoneState>(
              stream: isMobile ? PhoneState.stream : null,
              builder: (context, snapshot) {
                if (isMobile) {
                  return _buildDialogContent(context, themeState, colorTheme, textTheme, snapshot.data);
                } else {
                  return _buildDialogContent(context, themeState, colorTheme, textTheme, null);
                }
              });
        },
      ),
    );
  }

  Widget _buildDialogContent(
      BuildContext context, ThemeState themeState, ColorTheme colorTheme, TextTheme textTheme, PhoneState? status) {
    if (status?.status == PhoneStateStatus.CALL_INCOMING) {
      _homeCubit.hold(janusCubit: _janusCubit);

      if (context.mounted) {
        showOnholdCallDialog(
          context,
          onTap: () async {
            await _homeCubit.unhold(janusCubit: _janusCubit);
            pop();
          },
        );
      }
    }

    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(32.0),
        ),
      ),
      insetPadding: const EdgeInsets.all(10),
      backgroundColor: const Color.fromARGB(255, 54, 54, 54),
      content: SizedBox(
        width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            showNumpad
                ? const SizedBox.shrink()
                : AvatarGlow(
                    glowColor: const Color.fromARGB(255, 109, 109, 109),
                    // endRadius: context.deviceWidth(0.1),
                    child: Material(
                      // Replace this child with your own
                      elevation: 3.0,
                      shape: const CircleBorder(),
                      child: CircleAvatar(
                        radius: context.responsiveSize<double>(
                          moileSize: 70,
                          tabletSize: 70,
                          desktopSize: context.deviceWidth(0.03),
                          largeScreenSize: context.deviceWidth(0.02),
                        ),
                        backgroundColor: const Color.fromARGB(255, 83, 83, 83),
                        child: Icon(
                          Icons.call,
                          color: Colors.orange,
                          size: context.responsiveSize<double>(
                            moileSize: 70,
                            tabletSize: 70,
                            desktopSize: context.deviceWidth(0.03),
                            largeScreenSize: context.deviceWidth(0.02),
                          ),
                        ),
                      ),
                      // radius: 30.0,
                    ),
                  ),
            Column(
              children: [
                Text(
                  widget.callerID!,
                  style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                ),
                SizedBox(height: context.deviceHeight(0.02)),
                BlocBuilder<JanusCubit, JanusState>(
                  builder: (context, janusState) {
                    return Text(
                      janusState.statusMessage,
                      style: const TextStyle(color: Colors.orange),
                    );
                  },
                ),
                showNumpad
                    ? const SizedBox.shrink()
                    : StreamBuilder<int>(
                        stream: widget.stopWatchTimer.rawTime,
                        initialData: 0,
                        builder: (context, snap) {
                          final value = snap.data;

                          _displayTime = StopWatchTimer.getDisplayTime(value!);

                          return Column(
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(8),
                                child: Text(
                                  _displayTime,
                                  style: const TextStyle(
                                    fontSize: 15,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
              ],
            ),
            showNumpad
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      BlocBuilder<ThemeCubit, ThemeState>(
                        builder: (context, themeState) {
                          final colorTheme = themeState.colorTheme;
                          final textTheme = themeState.themeData.textTheme;

                          return CommonTextField(
                            // onFocusChange: (value) {
                            //   if (value) {
                            //     _focusCubit.focusDialpadField();
                            //   } else {
                            //     _focusCubit.unfocusDialpadField();
                            //   }
                            // },
                            // focusNode: _focusCubit.dialpadNode,
                            readOnly: true,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            textEditingController: _numberController,
                            scrollController: _scrollController,
                            textFieldHeight: heightLarge,
                            textAlign: TextAlign.center,
                            textStyle: textTheme.displayLarge!.copyWith(color: colorTheme.onPrimaryColor),
                          );
                        },
                      ),
                      const SizedBox(height: spacingExtraLarge),
                      Numpad(
                        callBack: (value) {
                          setState(() {
                            _numberController.text += value;
                          });

                          _homeCubit.sendDTMF(value);
                        },
                      ),
                    ],
                  )
                : Column(
                    children: [
                      Wrap(
                        alignment: WrapAlignment.center,
                        children: [
                          RoundShapeInkWell(
                            onTap: () {
                              showTransferDialpadDialog(context);
                            },
                            color: colorTheme.primaryColor,
                            contentWidget: const Icon(Icons.phone_forwarded),
                          ),
                          BlocBuilder<HomeCubit, HomeState>(
                            builder: (context, homeState) {
                              return RoundShapeInkWell(
                                onTap: () {
                                  _homeCubit.toggleMic(janusCubit: _janusCubit);
                                },
                                color: colorTheme.primaryColor,
                                contentWidget: homeState.isMicOn ? const Icon(Icons.mic) : const Icon(Icons.mic_off),
                              );
                            },
                          ),
                          RoundShapeInkWell(
                            color: colorTheme.primaryColor,
                            contentWidget: AudioDeviceSelector(
                              janusCubit: _janusCubit,
                              colorTheme: colorTheme,
                            ),
                          ),
                          // SizedBox(width: context.deviceWidth(0.03)),
                          RoundShapeInkWell(
                            onTap: () async {
                              await _homeCubit.hold(janusCubit: _janusCubit);

                              if (context.mounted) {
                                showOnholdCallDialog(
                                  context,
                                  onTap: () async {
                                    await _homeCubit.unhold(janusCubit: _janusCubit);
                                    pop();
                                  },
                                );
                              }
                            },
                            color: colorTheme.primaryColor,
                            contentWidget: const Icon(Icons.pause),
                          ),
                        ],
                      ),
                      RoundShapeInkWell(
                        onTap: () {
                          setState(() {
                            showNumpad = true;
                          });
                        },
                        color: colorTheme.primaryColor,
                        contentWidget: const Icon(Icons.dialpad),
                      ),
                    ],
                  ),
            // SizedBox(height: context.deviceWidth(0.03)),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: RoundShapeInkWell(
                    onTap: () {
                      popUntilInitial();

                      _homeCubit.hangup(janusCubit: _janusCubit);

                      // CallKitService.ongoing().endCall();
                    },
                    checkNetwork: false,
                    color: colorTheme.errorColor,
                    contentWidget: const Icon(Icons.call_end),
                  ),
                ),
                Expanded(
                  child: showNumpad
                      ? Align(
                          alignment: Alignment.centerLeft,
                          child: TextButton(
                              onPressed: () {
                                setState(() {
                                  showNumpad = false;
                                });
                              },
                              child: const Text('Hide Numpad')),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
