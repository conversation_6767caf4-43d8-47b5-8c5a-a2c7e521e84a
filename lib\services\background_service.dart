import 'package:collection/collection.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/home/<USER>/janus_cubit.dart';
import 'package:ddone/events/background_janus_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

enum CallkitAction {
  accept,
  decline;

  static CallkitAction? fromString(String? value) {
    return CallkitAction.values.firstWhereOrNull((s) => s.name == value);
  }
}

class BackgroundService with PrefsAware {
  late final JanusService janusService;
  late final CallkitService callkitService;
  late final IEventBus eventBus;

  static bool fromBackgroundIsolate = false;

  BackgroundService()
      : janusService = sl.get<JanusService>(),
        callkitService = sl.get<CallkitService>(),
        eventBus = sl.get<IEventBus>();

  Future<void> handleFirebaseBackgroundMessage(RemoteMessage remoteMessage) async {
    // no need to run when app is open. Sometimes this function will be triggered
    // when user just opened the app.
    if (await isAppOpen()) return;

    String? messageId = remoteMessage.messageId;

    // prevent same mesage get processed more than once.
    String prefsMessageIdKey = '${CacheKeys.firebaseMessageId}_$messageId';
    await prefs.reload();
    if (prefs.getBool(prefsMessageIdKey) == true) {
      log.t('SKIPPED handleFirebaseBackgroundMessage for messageId:$messageId');
      return;
    }
    await prefs.setBool(prefsMessageIdKey, true, ttlInSeconds: 60);
    log.t('handleFirebaseBackgroundMessage for messageId:$messageId');

    String? category = remoteMessage.data['category'];
    String caller = remoteMessage.data['caller'] ?? '';
    String callerId = remoteMessage.data['callerId'] ?? '';
    switch (category) {
      case 'call':
        {
          // ios use APNS PushKit, handled in native iOS swift code.
          if (isAndroid) {
            // android run callkit in background isolate, the init in main isolate will not work
            // so need to do it here.
            callkitService.init(
              acceptedCallCallback: (callDirection) {
                if (callDirection == CallDirection.incoming) {
                  // accept call need to be performed in main app isolate, because background isolate
                  // can only live for 30sec. So set into shared prefs and let the main app to handle it.
                  log.t('in background service accept');
                  prefs.setString(CacheKeys.callkitAction, CallkitAction.accept.name, ttlInSeconds: 30);
                }
              },
              declinedCallCallback: (callDirection) async {
                if (callDirection == CallDirection.incoming) {
                  log.t('in background service decline');
                  if (await isAppOpen()) {
                    // case where user open the app before
                    prefs.setString(CacheKeys.callkitAction, CallkitAction.decline.name, ttlInSeconds: 30);
                  } else {
                    await declineCall();
                  }
                }
              },
              endedCallCallback: () {},
              missedCallCallback: () {},
            );
            callkitService.incomingCall(caller, callerId);
          }
          break;
        }
      case 'miss-call':
        {
          // user didn't accept call in all devices
          callkitService.endAllCall();
          callkitService.showMissCall(caller, callerId);
          await Future.wait([
            prefs.setString(CacheKeys.missCall, 'miss call !!!!!'),
            prefs.setString(CacheKeys.missCallName, caller),
            prefs.setString(CacheKeys.missCallId, callerId),
            prefs.setString(CacheKeys.missCallTime, '${DateTime.now()}'),
          ]);
          break;
        }
      case 'end-call':
        {
          // user accepted call in other device, this device ringing should get ended.
          callkitService.endAllCall();
          break;
        }
      default:
        throw Exception('Unhandled category=$category in message');
    }
  }

  Future<void> acceptCall() async {
    if (!userHasLoggedIn()) return;

    try {
      bool setupResult = await _setupJanus();
      if (setupResult) {
        await janusService.initLocalStream();
        await janusService.acceptCall();
        callkitService.acceptedCall = true;
      }
    } catch (e) {
      log.e('Failed to accept call', error: e);
      callkitService.endCall();
      await janusService.dispose();
    }
  }

  Future<void> declineCall() async {
    if (!userHasLoggedIn()) return;

    try {
      bool setupResult = await _setupJanus();
      if (setupResult) {
        await janusService.declineCall();
      }
    } catch (e) {
      log.e('Failed to decline call', error: e);
      callkitService.endCall();
    } finally {
      await janusDispose();
    }
  }

  Future<void> hangupCall() async {
    try {
      await janusService.hangupCall();
    } catch (e) {
      log.e('Failed to hangup call', error: e);
      callkitService.endCall();
    } finally {
      await janusDispose();
    }
  }

  Future<void> iosCallToggleMic(bool isMuted) async {
    await janusService.muteMicAudio(isMuted);
  }

  Future<void> holdCall() async {
    await janusService.toggleHoldCall();
  }

  Future<bool> _setupJanus() async {
    String sipWsUrl = prefs.getString(CacheKeys.sipWsUrl)!;
    String sipNumber = prefs.getString(CacheKeys.sipNumber)!;
    String sipDomain = prefs.getString(CacheKeys.sipDomain)!;
    String sipProxy = prefs.getString(CacheKeys.sipProxy)!;
    String sipSecret = prefs.getString(CacheKeys.sipPwd)!;
    String sipName = prefs.getString(CacheKeys.sipName)!;
    janusService.fromBackground = true;
    bool initResult = await janusService.init(sipWsUrl);
    if (initResult) {
      await janusService.initMedia();
      janusService.listenSipEvent(
        sipRegisteredEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipRegisteredEvent).toString()));
        },
        sipIncomingCallEventCallback: (callerId, caller) async {
          eventBus.fire(BackgroundJanusEvent((JanusSipIncomingCallEvent).toString()));
          prefs.remove(CacheKeys.callkitStartCallTime);
        },
        sipAcceptedEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipAcceptedEvent).toString()));
          callkitService.connectedCall();
          prefs.setInt(CacheKeys.callkitStartCallTime, DateTime.now().millisecondsSinceEpoch);
        },
        sipProgressEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipProgressEvent).toString()));
        },
        sipCallingEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipCallingEvent).toString()));
          prefs.remove(CacheKeys.callkitStartCallTime);
        },
        sipProceedingEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipProceedingEvent).toString()));
        },
        sipRingingEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipRingingEvent).toString()));
        },
        sipHangupEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipHangupEvent).toString()));
          callkitService.endCall();
          prefs.remove(CacheKeys.callkitStartCallTime);
          await janusDispose();
        },
        sipUnRegisteredEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipUnregisteredEvent).toString()));
        },
        sipTransferCallEventCallback: () async {
          eventBus.fire(BackgroundJanusEvent((JanusSipTransferCallEvent).toString()));
        },
        sipMissedCallEventCallback: () async {},
        sipErrorCallback: (error) async {},
      );
      String? token = prefs.getString(CacheKeys.sipHeaderToken);
      await janusService.register('sip:$sipNumber@$sipDomain', 'sip:$sipProxy', sipSecret, sipName, token);
    }
    return initResult;
  }

  Future<void> janusDispose() async {
    await janusService.dispose();
    eventBus.fire(BackgroundJanusEvent((JanusSipUnregisteredEvent).toString()));
  }
}
