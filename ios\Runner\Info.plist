<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true />
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>$(BUNDLE_DISPLAY_NAME)</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(BUNDLE_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(CURRENT_PROJECT_VERSION)</string>
    <key>FirebaseMessagingAutoInitEnabled</key>
    <string>NO</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false />
    <key>LSRequiresIPhoneOS</key>
    <true />
    <key>LSSupportsOpeningDocumentsInPlace</key>
    <true />
    <key>NSAppleMusicUsageDescription</key>
    <string>${PRODUCT_NAME} need access to your Apple Music library to provide a better music
      experience.</string>
    <key>NSCameraUsageDescription</key>
    <string>${PRODUCT_NAME} need access to camera to scan QR code</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>${PRODUCT_NAME} need access to microphone to make call</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>${PRODUCT_NAME} need access to photos to send images in chat</string>
    <key>NSContactsUsageDescription</key>
    <string>${PRODUCT_NAME} need access to contacts to include your local phone book in the app</string>
    <key>PermissionGroupNotification</key>
    <string>${PRODUCT_NAME} need access to notification to send you notifications</string>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true />
    <key>UIBackgroundModes</key>
    <array>
      <string>fetch</string>
      <string>remote-notification</string>
      <string>voip</string>
    </array>
    <key>UIFileSharingEnabled</key>
    <true />
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false />
    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>itms-apps</string>
      <string>https</string>
    </array>
  </dict>
</plist>